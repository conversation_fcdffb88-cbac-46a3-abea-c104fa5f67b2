module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: ['eslint:recommended', 'prettier'],
  ignorePatterns: [
    'node_modules/',
    'uni_modules/',
    'unpackage/',
    'dist/',
    'build/',
    '*.min.js',
    '*.bundle.js',
    'static/iconfont/'
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module'
  },
  plugins: ['vue', 'prettier'],
  globals: {
    uni: 'readonly',
    wx: 'readonly',
    getCurrentPages: 'readonly',
    getApp: 'readonly',
    App: 'readonly',
    Page: 'readonly',
    Component: 'readonly',
    Behavior: 'readonly',
    __wxConfig: 'readonly'
  },
  rules: {
    // Prettier 相关
    'prettier/prettier': 'warn',

    // 较为宽松的规则配置
    'no-console': 'off', // 允许使用 console
    'no-debugger': 'warn', // debugger 只是警告
    'no-unused-vars': 'warn', // 未使用变量只警告
    'prefer-const': 'warn', // 优先使用 const 只警告
    'no-var': 'warn', // 不使用 var 只警告
    eqeqeq: 'off', // 不强制使用 ===
    'no-empty': 'warn', // 空代码块只警告
    'no-irregular-whitespace': 'warn', // 不规则空白字符只警告
    'no-prototype-builtins': 'off', // 允许直接调用原型方法
    'no-case-declarations': 'off', // 允许在 case 中声明变量
    'no-fallthrough': 'off', // 允许 switch case 穿透
    'no-useless-escape': 'warn', // 无用转义只警告
    'no-async-promise-executor': 'warn', // Promise 构造函数中的 async 只警告
    'no-misleading-character-class': 'warn', // 误导性字符类只警告
    'no-import-assign': 'warn', // 导入赋值只警告
    'no-dupe-else-if': 'warn', // 重复 else if 只警告
    'no-setter-return': 'warn', // setter 返回值只警告
    'prefer-regex-literals': 'off', // 不强制使用正则字面量

    // Vue 相关宽松规则
    'vue/multi-word-component-names': 'off', // 允许单词组件名
    'vue/no-v-html': 'off', // 允许使用 v-html
    'vue/require-default-prop': 'off', // 不强制 prop 默认值
    'vue/require-prop-types': 'off', // 不强制 prop 类型
    'vue/no-unused-components': 'warn', // 未使用组件只警告
    'vue/no-unused-vars': 'warn', // Vue 模板中未使用变量只警告
    'vue/no-multiple-template-root': 'off', // 允许多个根元素（Vue 3）
    'vue/singleline-html-element-content-newline': 'off', // 单行元素内容不强制换行
    'vue/multiline-html-element-content-newline': 'off', // 多行元素内容不强制换行
    'vue/html-self-closing': 'off', // 不强制自闭合标签
    'vue/max-attributes-per-line': 'off' // 不限制每行属性数量
  }
}
