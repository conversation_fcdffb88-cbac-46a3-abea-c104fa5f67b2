# 代码格式化配置指南

本项目已配置了完整的代码格式化工具链，包括 ESLint、Prettier 和 Stylelint。

## 工具说明

### ESLint

- **作用**: JavaScript/Vue 代码质量检查
- **配置文件**: `.eslintrc.cjs`
- **特点**: 采用较为宽松的规则，适合快速开发

### Prettier

- **作用**: 代码格式化
- **配置文件**: `.prettierrc`
- **特点**: 统一代码风格，自动格式化

### Stylelint

- **作用**: CSS/SCSS 样式检查
- **配置文件**: `.stylelintrc.js`
- **特点**: 基础样式规则检查

## 可用命令

### 代码检查

```bash
# 运行 ESLint 检查
pnpm run lint

# 自动修复 ESLint 问题
pnpm run lint:fix

# 运行 Stylelint 检查
pnpm run lint:style

# 自动修复 Stylelint 问题
pnpm run lint:style:fix
```

### 代码格式化

```bash
# 格式化所有支持的文件
pnpm run format

# 检查格式化状态（不修改文件）
pnpm run format:check
```

## VSCode 集成

项目已配置 VSCode 设置（`.vscode/settings.json`），包括：

- 保存时自动格式化
- 粘贴时自动格式化
- ESLint 和 Stylelint 自动修复
- 统一的格式化器设置

### 推荐的 VSCode 插件

1. **ESLint** - 代码质量检查
2. **Prettier - Code formatter** - 代码格式化
3. **Stylelint** - 样式检查
4. **Vetur** 或 **Volar** - Vue 支持

## 配置特点

### ESLint 规则（宽松配置）

- 允许使用 `console.log`
- 允许使用 `any` 类型
- 允许单词组件名
- 未使用变量只警告，不报错
- 支持 uni-app 全局变量

### Prettier 配置

- 不使用分号
- 使用单引号
- 不使用尾随逗号
- 行宽限制 100 字符
- 使用 2 空格缩进

### 忽略文件

- `node_modules/`
- `uni_modules/`
- `unpackage/`
- `dist/`
- 构建输出和临时文件

## 使用建议

1. **开发时**: 依赖 VSCode 自动格式化和错误提示
2. **提交前**: 运行 `pnpm run lint:fix` 和 `pnpm run format`
3. **CI/CD**: 可以添加格式检查到构建流程中

## 自定义配置

如需调整规则，可以修改对应的配置文件：

- ESLint: `.eslintrc.cjs`
- Prettier: `.prettierrc`
- Stylelint: `.stylelintrc.js`

## 故障排除

### 常见问题

1. **ESLint 报错**: 检查是否安装了所有依赖
2. **格式化不生效**: 确认 VSCode 插件已安装并启用
3. **样式检查问题**: Stylelint 配置较为宽松，大部分问题会被忽略

### 重新安装依赖

```bash
# 删除 node_modules 和 lock 文件
rm -rf node_modules pnpm-lock.yaml

# 重新安装
pnpm install
```
