<script>
export default {
  onLaunch: function () {
    console.log('App Launch')
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  }
}
</script>

<style lang="scss">
/*每个页面公共css */
@import '@/static/iconfont/iconfont.css';
@import '@/static/parameters.scss';
@import '@/static/free.scss';
//
// 设置整个项目的背景色
page {
  // background-color: $light;
  font-size: 32rpx;
}
/*form 一些通用样式*/
.normal-button-rounded {
  $h: 80rpx;
  height: $h;
  border-radius: $h;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

/*每个页面公共css */
</style>
