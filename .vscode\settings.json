{"editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.formatOnType": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "stylelint.validate": ["css", "scss", "vue"], "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "emmet.includeLanguages": {"vue-html": "html"}, "vetur.validation.template": false, "vetur.validation.script": false, "vetur.validation.style": false}