{"name": "business-card-mini-program", "version": "1.0.0", "description": "名片小程序", "main": "main.js", "scripts": {"lint": "eslint --ext .js,.vue,.ts .", "lint:fix": "eslint --ext .js,.vue,.ts . --fix", "format": "prettier --write \"**/*.{js,vue,ts,json,css,scss,html,md}\"", "format:check": "prettier --check \"**/*.{js,vue,ts,json,css,scss,html,md}\""}, "keywords": ["uni-app", "vue3", "miniprogram", "business-card"], "author": "", "license": "MIT", "devDependencies": {"@eslint/js": "^9.34.0", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "@vue/eslint-config-typescript": "^14.6.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-config-standard": "^17.1.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.6.2", "stylelint": "^16.23.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^39.0.0", "stylelint-scss": "^6.12.1"}, "dependencies": {"pinia": "^2.1.7"}}